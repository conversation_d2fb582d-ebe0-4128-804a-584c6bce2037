# AI量化交易工具系统

[![Python Version](https://img.shields.io/badge/python-3.9+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Code Style](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

一个专业的AI量化交易工具系统，提供完整的量化交易解决方案，包括数据管理、策略开发、回测分析、实时交易和风险管理等核心功能。

## 🚀 主要特性

- **高性能架构**：基于FastAPI和异步编程，支持高并发和低延迟
- **完整的数据管理**：支持多数据源接入，实时数据处理和质量监控
- **灵活的策略开发**：在线代码编辑器，丰富的技术指标库
- **专业的回测分析**：历史数据回测，详细的性能分析报告
- **实时交易执行**：对接主流券商API，支持多种订单类型
- **智能风险管理**：实时风险监控，可配置的风控规则
- **直观的用户界面**：响应式Web界面，丰富的数据可视化
- **企业级安全**：多因子认证，基于角色的权限控制

### 🆕 最新功能更新

#### 用户认证与权限管理系统
- ✅ **用户注册与邮箱验证**：完整的用户注册流程，支持邮箱验证
- ✅ **安全登录系统**：支持记住登录状态，自动刷新令牌
- ✅ **RBAC权限模型**：基于角色的访问控制，细粒度权限管理
- ✅ **权限检查中间件**：API级别的权限验证和控制
- ✅ **角色管理界面**：可视化的角色创建、编辑和权限分配
- ✅ **权限管理界面**：用户角色分配和权限监控

#### 策略编辑器完善
- ✅ **Monaco Editor集成**：专业级代码编辑器，支持多种主题
- ✅ **Python语法高亮**：完整的Python语法高亮和着色
- ✅ **智能代码补全**：量化交易API函数的智能提示和补全
- ✅ **语法错误检查**：实时语法检查和错误提示
- ✅ **代码格式化**：符合PEP 8规范的代码自动格式化
- ✅ **策略调试功能**：断点设置、变量监控、执行跟踪
- ✅ **策略模板库**：丰富的策略模板，快速开始策略开发

#### 🆕 策略库大幅扩展
- ✅ **技术指标策略**：新增MACD策略，经典趋势跟踪算法
- ✅ **统计套利策略**：配对交易策略，市场中性获取稳定收益
- ✅ **因子投资策略**：多因子模型，基于价值、成长、质量等因子选股
- ✅ **网格交易策略**：震荡市场专用，机械化高频交易
- ✅ **机器学习策略**：支持向量机策略，AI驱动的价格预测
- ✅ **策略分类优化**：新增5个策略分类，覆盖主流量化交易方法
- ✅ **策略文档完善**：每个策略配备详细的技术文档和使用指南

## 📋 系统要求

- Python 3.9+
- PostgreSQL 12+
- ClickHouse 22+
- Redis 6+
- Docker & Docker Compose (可选)

## 🛠️ 快速开始

### 方式一：使用启动脚本（推荐）

```bash
# 1. 克隆项目
git clone https://github.com/aier/ai-quantitative-tools.git
cd ai-quantitative-tools

# 2. 设置环境
./scripts/start.sh setup

# 3. 运行测试
./scripts/start.sh test

# 4. 启动开发服务器
./scripts/start.sh dev
```

### 方式二：使用Docker Compose

```bash
# 1. 克隆项目
git clone https://github.com/aier/ai-quantitative-tools.git
cd ai-quantitative-tools

# 2. 启动完整系统
./scripts/start.sh docker

# 3. 访问系统
# - 主应用: http://localhost:8000
# - API文档: http://localhost:8000/docs
# - 监控面板: http://localhost:3000
```

### 方式三：手动安装

```bash
# 1. 创建虚拟环境
python3 -m venv venv
source venv/bin/activate

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑.env文件配置数据库等参数

# 4. 启动应用
python main.py
```

## 📚 项目结构

```
ai-quantitative-tools/
├── docs/                   # 项目文档
│   ├── 需求分析文档.md
│   ├── 系统架构设计文档.md
│   ├── 技术选型与数据库设计文档.md
│   ├── API接口设计文档.md
│   ├── 安全设计与监控机制文档.md
│   ├── 功能清单与开发计划文档.md
│   └── 项目总结文档.md
├── src/                    # 源代码
│   └── quantitative_tools/
│       ├── __init__.py
│       ├── config.py       # 配置管理
│       ├── core/           # 核心模块
│       ├── api/            # API接口
│       ├── models/         # 数据模型
│       ├── services/       # 业务服务
│       └── utils/          # 工具函数
├── tests/                  # 测试代码
├── scripts/                # 脚本文件
├── docker-compose.yml      # Docker编排
├── Dockerfile             # Docker镜像
├── requirements.txt       # Python依赖
├── pyproject.toml         # 项目配置
├── TODO.md               # 开发任务清单
└── README.md             # 项目说明
```

## 🔧 配置说明

系统支持通过环境变量或`.env`文件进行配置。主要配置项包括：

### 数据库配置
```bash
# PostgreSQL主数据库
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_password
POSTGRES_DB=quantitative_tools

# ClickHouse时序数据库
CLICKHOUSE_HOST=localhost
CLICKHOUSE_PORT=9000
CLICKHOUSE_DB=market_data

# Redis缓存
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 安全配置
```bash
# JWT密钥（生产环境必须修改）
SECRET_KEY=your-super-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=15
```

### 数据源配置
```bash
# Tushare数据源
TUSHARE_ENABLED=true
TUSHARE_TOKEN=your_tushare_token

# Wind数据源
WIND_ENABLED=false
WIND_USERNAME=your_username
WIND_PASSWORD=your_password
```

完整的配置说明请参考 [.env.example](.env.example) 文件。

## 📖 文档

- [需求分析文档](docs/需求分析文档.md) - 详细的功能需求和技术需求
- [系统架构设计](docs/系统架构设计文档.md) - 整体架构和技术选型
- [API接口文档](docs/API接口设计文档.md) - RESTful API规范
- [数据库设计](docs/技术选型与数据库设计文档.md) - 数据模型和存储策略
- [安全设计](docs/安全设计与监控机制文档.md) - 安全架构和监控机制
- [开发计划](docs/功能清单与开发计划文档.md) - 功能清单和开发时间表

## 🧪 测试

```bash
# 运行所有测试
./scripts/start.sh test

# 或者手动运行
python -m pytest tests/ -v

# 运行特定测试
python -m pytest tests/test_basic.py -v

# 生成测试覆盖率报告
python -m pytest tests/ --cov=src --cov-report=html
```

## 🚀 部署

### 开发环境
```bash
./scripts/start.sh dev
```

### 生产环境
```bash
# 使用Docker Compose
./scripts/start.sh docker

# 或者手动部署
export ENVIRONMENT=production
export DEBUG=false
python main.py
```

## 📊 监控

系统集成了完整的监控体系：

- **Prometheus**: 指标收集 (http://localhost:9090)
- **Grafana**: 可视化面板 (http://localhost:3000)
- **ELK Stack**: 日志分析
- **Jaeger**: 链路追踪

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- 项目主页: https://github.com/aier/ai-quantitative-tools
- 问题反馈: https://github.com/aier/ai-quantitative-tools/issues
- 邮箱: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户！

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！